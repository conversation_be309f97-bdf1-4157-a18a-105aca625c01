# 拉取基础镜像
FROM python:3.10.9

# 定义版本号
ARG VERSION=0.0.1


# 创建项目工作区
WORKDIR /app

COPY ./requirements_torch.txt /app/requirements_torch.txt
RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple

RUN pip3 install -r requirements_torch.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
# 将依赖文件复制进工作区
ADD janus.txt /app/janus.txt
RUN pip3 install -r janus.txt -i https://pypi.tuna.tsinghua.edu.cn/simple




COPY . /app


EXPOSE 9000

# 启动服务
CMD ["python","fastapi_demo.py"]

# docker 启动命令
# docker run -d --name deepseek-janus-service --gpus='"device=3"' -p 9000:9000 -v /root/project/models/Janus-Pro-7B:/app/model  deepseek-janus