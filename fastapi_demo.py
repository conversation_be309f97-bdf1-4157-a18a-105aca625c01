#!/usr/bin/python
# -*- coding: UTF-8 -*-
from fastapi import FastAPI, File, Form, UploadFile, HTTPException
from fastapi.responses import JSONResponse, StreamingResponse
import torch
from transformers import AutoConfig, AutoModelForCausalLM,TextIteratorStreamer
from janus.models import MultiModalityCausalLM, VLChatProcessor
from PIL import Image
import numpy as np
import io
import base64
from pydantic import BaseModel,Field
import json
from sse_starlette.sse import EventSourceResponse
from fastapi.concurrency import run_in_threadpool
import asyncio
import threading
from datetime import datetime
import uuid
from transformers import LlavaForConditionalGeneration

app = FastAPI()

# Load model and processor
model_path = r"./model"
config = AutoConfig.from_pretrained(model_path)
language_config = config.language_config
language_config._attn_implementation = 'eager'
vl_gpt = AutoModelForCausalLM.from_pretrained(model_path,
                                              language_config=language_config,
                                              trust_remote_code=True
                                              )
vl_gpt = vl_gpt.to(torch.float16).cuda()

vl_chat_processor = VLChatProcessor.from_pretrained(model_path)
tokenizer = vl_chat_processor.tokenizer
cuda_device = 'cuda' if torch.cuda.is_available() else 'cpu'


class Request(BaseModel):
    model: str = Field(default="Janus-Pro-7B")
    messages: list[dict]
    stream: bool = Field(default=True)
    seed: int = Field(default=42)
    top_p: float = Field(default=0.9)
    temperature: float = Field(default=0.1)


# 公共预处理函数
def prepare_common_inputs(messages: list, seed: int):
    """处理公共输入逻辑，返回模型需要的输入嵌入和注意力掩码"""
    user_msg = next((m for m in messages if m["role"].lower() == "user"), None)
    if not user_msg:
        raise ValueError("No user message found in the input")

    question = user_msg.get("content", "")
    base64_images = user_msg.get("images", [])
    if not base64_images:
        raise ValueError("No image found in user message")

    try:
        image_data = base64.b64decode(base64_images[0])
    except Exception as e:
        raise ValueError("Invalid base64 image data") from e

    image = Image.open(io.BytesIO(image_data))
    if image.mode == 'RGBA':
        image = image.convert('RGB')
    pil_images = [image]

    torch.cuda.empty_cache()
    torch.manual_seed(seed)
    np.random.seed(seed)
    torch.cuda.manual_seed(seed)

    conversation = [
        {
            "role": "User",
            "content": f"<image_placeholder>\n{question}",
            "images": [image_data],
        },
        {"role": "Assistant", "content": ""},
    ]
    # 使用tokenizer统计输入文本的token数
    user_content = f"<image_placeholder>\n{question}"
    text_inputs = tokenizer(
        user_content,
        return_tensors="pt",
        add_special_tokens=False  # 避免重复统计特殊token
    )
    prompt_tokens = text_inputs.input_ids.shape[1]

    prepare_inputs = vl_chat_processor(
        conversations=conversation,
        images=pil_images,
        force_batchify=True
    ).to(cuda_device, dtype=torch.float16)

    return vl_gpt.prepare_inputs_embeds(**prepare_inputs), prepare_inputs.attention_mask,prompt_tokens


@torch.inference_mode()
def multimodal_understanding_v2_stream(messages, seed, top_p, temperature):
    inputs_embeds, attention_mask, prompt_tokens = prepare_common_inputs(messages, seed)

    accumulated_text = ""
    # 创建文本流式处理器
    streamer = TextIteratorStreamer(tokenizer, timeout=300.0, skip_prompt=True, skip_special_tokens=True)

    # 在单独线程中启动生成过程
    generation_kwargs = dict(
        inputs_embeds=inputs_embeds,
        attention_mask=attention_mask,
        pad_token_id=tokenizer.eos_token_id,
        bos_token_id=tokenizer.bos_token_id,
        eos_token_id=tokenizer.eos_token_id,
        max_new_tokens=512,
        do_sample=temperature > 0,
        temperature=temperature if temperature > 0 else None,
        top_p=top_p if temperature > 0 else None,
        use_cache=True,
        streamer=streamer
    )

    thread = threading.Thread(target=vl_gpt.language_model.generate, kwargs=generation_kwargs)
    thread.start()

    for new_text in streamer:
        accumulated_text += new_text
        yield json.dumps({"model": "Janus-Pro-7B",
                          "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                          "message": [{"role":"assistant","content": new_text}] ,
                          "done": False})
    # 生成完成后统计token
    completion_tokens = len(tokenizer.encode(accumulated_text, add_special_tokens=False))
    usage = {
        "prompt_tokens": prompt_tokens,
        "completion_tokens": completion_tokens,
        "total_tokens": prompt_tokens + completion_tokens
    }
    yield json.dumps({"model": "Janus-Pro-7B",
                      "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                      "message": [{"role": "assistant","content": ""}],
                      "usage": usage,
                      "done": True})


# 非流式生成函数
@torch.inference_mode()
def multimodal_non_stream_response(messages, seed, top_p, temperature):
    inputs_embeds, attention_mask, prompt_tokens = prepare_common_inputs(messages, seed)

    generation_kwargs = dict(
        inputs_embeds=inputs_embeds,
        attention_mask=attention_mask,
        pad_token_id=tokenizer.eos_token_id,
        bos_token_id=tokenizer.bos_token_id,
        eos_token_id=tokenizer.eos_token_id,
        max_new_tokens=512,
        do_sample=temperature > 0,
        temperature=temperature if temperature > 0 else None,
        top_p=top_p if temperature > 0 else None,
        use_cache=True
    )

    output_ids = vl_gpt.language_model.generate(**generation_kwargs)
    full_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)

    completion_tokens = 0
    for token_id in output_ids[0]:
        if token_id not in [tokenizer.eos_token_id, tokenizer.pad_token_id]:
            completion_tokens += 1

    usage = {
        "prompt_tokens": prompt_tokens,
        "completion_tokens": completion_tokens,
        "total_tokens": prompt_tokens + completion_tokens
    }

    return {
        "id": str(uuid.uuid4()),
        "model": "Janus-Pro-7B",
        "choices": [{"index": 0,
                     "message": {"role": "assistant", "content": full_text},
                     "finish_reason": "stop"}],
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "usage": usage,
        "done": True
    }


@app.post("/api/chat")
async def understand_image_and_question_sse(request: Request):

    if request.stream:
        return EventSourceResponse(multimodal_understanding_v2_stream(
            messages=request.messages,
            seed=request.seed,
            top_p=request.top_p,
            temperature=request.temperature
        ),
        ping=20)
    else:
        response = await run_in_threadpool(
            multimodal_non_stream_response,
            messages=request.messages,
            seed=request.seed,
            top_p=request.top_p,
            temperature=request.temperature
        )
        return JSONResponse(response)



def generate(input_ids,
             width,
             height,
             temperature: float = 1,
             parallel_size: int = 5,
             cfg_weight: float = 5,
             image_token_num_per_image: int = 576,
             patch_size: int = 16):
    torch.cuda.empty_cache()
    tokens = torch.zeros((parallel_size * 2, len(input_ids)), dtype=torch.int).to(cuda_device)
    for i in range(parallel_size * 2):
        tokens[i, :] = input_ids
        if i % 2 != 0:
            tokens[i, 1:-1] = vl_chat_processor.pad_id
    inputs_embeds = vl_gpt.language_model.get_input_embeddings()(tokens)
    generated_tokens = torch.zeros((parallel_size, image_token_num_per_image), dtype=torch.int).to(cuda_device)

    pkv = None
    for i in range(image_token_num_per_image):
        outputs = vl_gpt.language_model.model(inputs_embeds=inputs_embeds, use_cache=True, past_key_values=pkv)
        pkv = outputs.past_key_values
        hidden_states = outputs.last_hidden_state
        logits = vl_gpt.gen_head(hidden_states[:, -1, :])
        logit_cond = logits[0::2, :]
        logit_uncond = logits[1::2, :]
        logits = logit_uncond + cfg_weight * (logit_cond - logit_uncond)
        probs = torch.softmax(logits / temperature, dim=-1)
        next_token = torch.multinomial(probs, num_samples=1)
        generated_tokens[:, i] = next_token.squeeze(dim=-1)
        next_token = torch.cat([next_token.unsqueeze(dim=1), next_token.unsqueeze(dim=1)], dim=1).view(-1)
        img_embeds = vl_gpt.prepare_gen_img_embeds(next_token)
        inputs_embeds = img_embeds.unsqueeze(dim=1)
    patches = vl_gpt.gen_vision_model.decode_code(
        generated_tokens.to(dtype=torch.int),
        shape=[parallel_size, 8, width // patch_size, height // patch_size]
    )

    return generated_tokens.to(dtype=torch.int), patches


def unpack(dec, width, height, parallel_size=5):
    dec = dec.to(torch.float32).cpu().numpy().transpose(0, 2, 3, 1)
    dec = np.clip((dec + 1) / 2 * 255, 0, 255)

    visual_img = np.zeros((parallel_size, width, height, 3), dtype=np.uint8)
    visual_img[:, :, :] = dec

    return visual_img


@torch.inference_mode()
def generate_image(prompt, seed, guidance):
    torch.cuda.empty_cache()
    seed = seed if seed is not None else 12345
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    np.random.seed(seed)
    width = 384
    height = 384
    parallel_size = 5

    with torch.no_grad():
        messages = [{'role': 'User', 'content': prompt}, {'role': 'Assistant', 'content': ''}]
        text = vl_chat_processor.apply_sft_template_for_multi_turn_prompts(
            conversations=messages,
            sft_format=vl_chat_processor.sft_format,
            system_prompt=''
        )
        text = text + vl_chat_processor.image_start_tag
        input_ids = torch.LongTensor(tokenizer.encode(text))
        _, patches = generate(input_ids, width // 16 * 16, height // 16 * 16, cfg_weight=guidance,
                              parallel_size=parallel_size)
        images = unpack(patches, width // 16 * 16, height // 16 * 16)

        return [Image.fromarray(images[i]).resize((1024, 1024), Image.LANCZOS) for i in range(parallel_size)]


@app.post("/generate_images/")
async def generate_images(
        prompt: str = Form(...),
        seed: int = Form(None),
        guidance: float = Form(5.0),
):
    try:
        images = generate_image(prompt, seed, guidance)

        def image_stream():
            for img in images:
                buf = io.BytesIO()
                img.save(buf, format='PNG')
                buf.seek(0)
                yield buf.read()

        return StreamingResponse(image_stream(), media_type="multipart/related")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image generation failed: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9000)

